// 背景图配置文件
export const BACKGROUND_CONFIG = {
  // 首页Hero区域背景图
  hero: {
    primary: '/images/banners/home.jpg',
    fallback: '/images/banners/home.jpg',
    description: '西安城市天际线夕阳景观',
    overlay:
      'linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 50%, rgba(240, 147, 251, 0.6) 100%)',
  },

  // 山塬页面背景图
  mountain: {
    primary: '/images/banners/mountain.jpg',
    fallback: '/images/banners/mountain.jpg',
    description: '灰色岩石地貌，层次分明',
    overlay:
      'linear-gradient(135deg, rgba(139, 69, 19, 0.7) 0%, rgba(160, 82, 45, 0.7) 100%)',
  },

  // 水系页面背景图
  water: {
    primary: '/images/banners/water.jpg',
    fallback: '/images/banners/water.jpg',
    description: '绿色河流穿越树林',
    overlay:
      'linear-gradient(135deg, rgba(30, 144, 255, 0.7) 0%, rgba(0, 191, 255, 0.7) 100%)',
  },

  // 历史要素页面背景图
  history: {
    primary: '/images/banners/historical.jpg',
    fallback: '/images/banners/historical.jpg',
    description: '中国传统塔楼建筑',
    overlay:
      'linear-gradient(135deg, rgba(184, 134, 11, 0.7) 0%, rgba(146, 64, 14, 0.7) 100%)',
  },

  // 数字化页面背景图
  digital: {
    primary: '/images/banners/ancient-book-banner.jpg',
    fallback: '/images/banners/ancient-book-banner.jpg',
    description: '古代书籍文献',
    overlay:
      'linear-gradient(135deg, rgba(30, 144, 255, 0.7) 0%, rgba(0, 191, 255, 0.7) 100%)',
  },
};

// 背景图类型
export type BackgroundType = keyof typeof BACKGROUND_CONFIG;

// 获取背景图URL
export const getBackgroundUrl = (
  type: BackgroundType,
  useFallback = false,
): string => {
  const config = BACKGROUND_CONFIG[type];
  return useFallback ? config.fallback : config.primary;
};

// 获取背景图样式
export const getBackgroundStyle = (
  type: BackgroundType,
  useFallback = false,
) => {
  const config = BACKGROUND_CONFIG[type];
  const imageUrl = useFallback ? config.fallback : config.primary;

  return {
    backgroundImage: `${config.overlay}, url(${imageUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll',
  };
};

// 预加载背景图
export const preloadBackground = (type: BackgroundType): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const config = BACKGROUND_CONFIG[type];

    img.onload = () => resolve();
    img.onerror = () => {
      // 如果主图片加载失败，尝试加载备用图片
      const fallbackImg = new Image();
      fallbackImg.onload = () => resolve();
      fallbackImg.onerror = reject;
      fallbackImg.src = config.fallback;
    };

    img.src = config.primary;
  });
};

// 批量预加载所有背景图
export const preloadAllBackgrounds = async (): Promise<void> => {
  const types: BackgroundType[] = [
    'hero',
    'mountain',
    'water',
    'history',
    'digital',
  ];

  try {
    await Promise.all(types.map((type) => preloadBackground(type)));
    console.log('✅ 所有背景图预加载完成');
  } catch (error) {
    console.warn('⚠️ 部分背景图预加载失败:', error);
  }
};
