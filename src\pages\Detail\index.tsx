import NetworkGraph from '@/components/NetworkGraph';
import PublicLayout from '@/components/PublicLayout';
import type { DictionaryState } from '@/models/dictionary';
import type { RegionDict, TypeDict } from '@/pages/Admin/Dictionary/dict-types';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import {
  getPublicHistoricalElementDetail,
  getPublicHistoricalElementPhotos,
} from '@/services/historicalElement';
import {
  getPublicMountainDetail,
  getPublicMountainPhotos,
} from '@/services/mountain';
import {
  getPublicWaterSystemDetail,
  getPublicWaterSystemPhotos,
} from '@/services/waterSystem';
import { ArrowLeftOutlined, FileTextOutlined } from '@ant-design/icons';
import { connect, useParams } from '@umijs/max';
import { Button, Card, Divider, Empty, Image, message, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import './styles.less';

interface DetailPageProps {
  regionList: RegionDict[];
  typeList: TypeDict[];
  dispatch: any;
}

const DetailPage: React.FC<DetailPageProps> = ({
  regionList,
  typeList,
  dispatch,
}) => {
  const { type, id } = useParams();
  const numericId = Number(id);

  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<
    Array<{ id: number; name: string; url: string }>
  >([]);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [relationsLoading, setRelationsLoading] = useState<boolean>(false);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );

  const getTypeName = () => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'waterSystem':
        return '水系';
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  // 递归查找字典项
  const findDictItem = (list: any[], id: number): any => {
    for (const item of list) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = findDictItem(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取区域名称
  const getRegionName = (regionDictId?: number) => {
    if (!regionDictId || !regionList || regionList.length === 0) {
      return detail?.regionDict?.regionName || '-';
    }
    const regionItem = findDictItem(regionList, regionDictId);
    return regionItem?.regionName || detail?.regionDict?.regionName || '-';
  };

  // 获取类型名称
  const getTypeDictName = (typeDictId?: number) => {
    if (!typeDictId || !typeList || typeList.length === 0) {
      return detail?.typeDict?.typeName || '-';
    }
    const typeItem = findDictItem(typeList, typeDictId);
    return typeItem?.typeName || detail?.typeDict?.typeName || '-';
  };

  // 获取节点颜色
  const getNodeColor = (nodeType: string): string => {
    const colorMap: Record<string, string> = {
      mountain: '#8B4513', // 棕色 - 山塬
      water_system: '#4169E1', // 蓝色 - 水系
      historical_element: '#DC143C', // 红色 - 历史要素
      type_dict: '#32CD32', // 绿色 - 类型
      region_dict: '#FFD700', // 金色 - 区域
    };
    return colorMap[nodeType] || '#666';
  };

  // 获取连线颜色
  const getLinkColor = (relationName: string): string => {
    const colorMap: Record<string, string> = {
      选址关联: '#4169E1', // 蓝色
      视线关联: '#9932CC', // 紫色
      历史关联: '#DC143C', // 红色
      功能关联: '#32CD32', // 绿色
      其他关联: '#808080', // 灰色
    };
    return colorMap[relationName] || '#999';
  };

  // 获取目标要素的显示名称
  const getTargetElementName = (relation: API.ElementRelation) => {
    // 根据接口文档，优先使用 targetElement.name
    if (relation.targetElement) {
      return relation.targetElement.name;
    }
    // 如果没有 targetElement，使用 term 字段
    return relation.term || '未知要素';
  };

  // 获取目标要素类型的显示名称
  const getTargetElementTypeName = (relation: API.ElementRelation) => {
    // 根据接口文档，使用 targetEntityType 字段判断具体的要素类型
    switch (relation.targetEntityType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
        return '水系';
      case 'historical_element':
        return '历史要素';
      case 'type_dict':
        return '类型';
      case 'region_dict':
        return '区域';
      default:
        return '其他';
    }
  };

  // 获取源要素类型的显示名称
  const getSourceElementTypeName = (sourceType: string) => {
    switch (sourceType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
        return '水系';
      case 'historical_element':
        return '历史要素';
      default:
        return '其他';
    }
  };

  // 转换关联关系数据为网络图数据
  const convertRelationsToNetworkData = (
    relations: API.ElementRelation[],
    currentElement: any,
  ): API.NetworkGraphData => {
    const nodes: API.NetworkGraphNode[] = [];
    const links: API.NetworkGraphLink[] = [];
    const nodeMap = new Map<string, API.NetworkGraphNode>();

    // 添加当前要素作为中心节点
    const currentNodeId = `${type}_${numericId}`;
    const currentNode: API.NetworkGraphNode = {
      id: currentNodeId,
      name: currentElement?.name || '当前要素',
      type:
        type === 'waterSystem'
          ? 'water_system'
          : type === 'historicalElement'
          ? 'historical_element'
          : type || 'unknown',
      category: getTypeName(),
      size: 20, // 中心节点较大
      color:
        type === 'mountain'
          ? '#8B4513'
          : type === 'waterSystem'
          ? '#4169E1'
          : type === 'historicalElement'
          ? '#DC143C'
          : '#666',
    };
    nodeMap.set(currentNodeId, currentNode);

    // 处理关联关系
    relations.forEach((relation) => {
      // 添加源要素节点
      const sourceNodeId = `${relation.sourceType}_${relation.sourceId}`;
      if (!nodeMap.has(sourceNodeId)) {
        const sourceNode: API.NetworkGraphNode = {
          id: sourceNodeId,
          name: relation.sourceElement?.name || '未知要素',
          type:
            relation.sourceType === 'water_system'
              ? 'water_system'
              : relation.sourceType === 'historical_element'
              ? 'historical_element'
              : relation.sourceType || 'unknown',
          category: getSourceElementTypeName(relation.sourceType),
          size: 15,
          color: getNodeColor(relation.sourceType),
        };
        nodeMap.set(sourceNodeId, sourceNode);
      }

      // 添加目标要素节点
      const targetNodeId = `${relation.targetEntityType}_${relation.targetId}`;
      if (!nodeMap.has(targetNodeId)) {
        const targetNode: API.NetworkGraphNode = {
          id: targetNodeId,
          name: getTargetElementName(relation),
          type: relation.targetEntityType || 'unknown',
          category: getTargetElementTypeName(relation),
          size: 15,
          color: getNodeColor(relation.targetEntityType || 'unknown'),
        };
        nodeMap.set(targetNodeId, targetNode);
      }

      // 创建连线
      const link: API.NetworkGraphLink = {
        source: sourceNodeId,
        target: targetNodeId,
        relation: relation.relationDict?.relationName || '其他关系',
        direction: relation.direction,
        term: relation.term,
        weight: 1,
        color: getLinkColor(relation.relationDict?.relationName || '其他关系'),
      };
      links.push(link);
    });

    // 转换节点映射为数组
    nodes.push(...Array.from(nodeMap.values()));

    // 生成分类列表
    const categories = Array.from(new Set(nodes.map((node) => node.category)));

    return {
      nodes,
      links,
      categories,
    };
  };

  // 获取关联关系数据（带详情数据）
  const loadRelationsWithDetail = async (detailData: any) => {
    if (!type || !numericId) return;

    setRelationsLoading(true);
    try {
      // 根据接口文档，关联关系接口的路径参数
      const elementType =
        type === 'waterSystem'
          ? 'waterSystem' // 接口文档中使用 waterSystem
          : type === 'historicalElement'
          ? 'historicalElement' // 接口文档中使用 historicalElement
          : type; // mountain 保持不变

      const response = await getPublicElementRelationsByElement(
        elementType,
        numericId,
      );
      if (response.errCode === 0) {
        const relationData = response.data || [];
        setRelations(relationData);

        // 生成网络图数据
        if (relationData.length > 0) {
          console.log('🔗 关联关系数据:', relationData);
          const networkGraphData = convertRelationsToNetworkData(
            relationData,
            detailData,
          );
          console.log('📊 网络图数据:', networkGraphData);
          setNetworkData(networkGraphData);
        } else {
          // 如果没有关联关系数据，不显示网络图
          console.log('ℹ️ 无关联关系数据');
          setNetworkData(null);
        }
      }
    } catch (error: any) {
      console.error('加载关联关系失败:', error);
      message.error('加载关联关系失败');
    } finally {
      setRelationsLoading(false);
    }
  };

  // 按关系类型分组关联数据
  const getGroupedRelations = () => {
    const grouped: { [key: string]: API.ElementRelation[] } = {};
    relations.forEach((relation) => {
      const relationName = relation.relationDict?.relationName || '其他关系';
      if (!grouped[relationName]) {
        grouped[relationName] = [];
      }
      grouped[relationName].push(relation);
    });
    return grouped;
  };

  // 获取关系的方向描述
  const getRelationDirection = (relation: API.ElementRelation) => {
    const currentElementName = detail?.name;

    // 双向关系
    if (relation.direction === '双向') {
      return '↔';
    }

    // 单向关系：判断当前要素是源还是目标
    if (relation.sourceElement?.name === currentElementName) {
      // 当前要素是源，箭头指向目标
      return '→';
    } else {
      // 当前要素是目标，箭头来自源
      return '←';
    }
  };

  // 获取关系类型对应的颜色
  const getRelationColor = (relationName: string) => {
    const colorMap: { [key: string]: string } = {
      地理关系: 'blue',
      历史关系: 'green',
      文化关系: 'purple',
      空间关系: 'orange',
      时间关系: 'red',
      其他关系: 'default',
    };
    return colorMap[relationName] || 'default';
  };

  // 初始化字典数据
  useEffect(() => {
    if (!regionList || regionList.length === 0) {
      dispatch({ type: 'dictionary/fetchRegionList' });
    }
    if (!typeList || typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dispatch, regionList, typeList]);

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        let detailData = null;
        if (type === 'mountain') {
          const [d, p] = await Promise.all([
            getPublicMountainDetail(numericId),
            getPublicMountainPhotos(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'waterSystem') {
          const [d, p] = await Promise.all([
            getPublicWaterSystemDetail(numericId),
            getPublicWaterSystemPhotos(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'historicalElement') {
          const [d, p] = await Promise.all([
            getPublicHistoricalElementDetail(numericId),
            getPublicHistoricalElementPhotos(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        }

        // 在详情加载完成后加载关联关系
        if (detailData) {
          await loadRelationsWithDetail(detailData);
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [type, numericId]);

  // 获取banner图片
  const getBannerImage = () => {
    // 优先使用关联的图片
    if (photos && photos.length > 0) {
      return photos[0].url;
    }

    // 使用本地的默认背景图片
    const defaultImages = {
      mountain: '/images/banners/mountain.jpg',
      waterSystem: '/images/banners/water.jpg',
      historicalElement: '/images/banners/historical.jpg',
      default: '/images/banners/forest.jpg',
    };

    return (
      defaultImages[type as keyof typeof defaultImages] || defaultImages.default
    );
  };

  return (
    <div className="detail-page">
      <PublicLayout>
        {/* Banner区域 */}
        <div className="banner-section">
          <div
            className="banner-image"
            style={{
              backgroundImage: `url(${getBannerImage()})`,
            }}
          >
            <div className="banner-overlay">
              <div className="banner-content">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => window.history.back()}
                  className="back-button"
                >
                  返回
                </Button>

                <div className="banner-title-section">
                  <h1 className="page-title">
                    {detail?.name || getTypeName()}
                  </h1>
                  {detail?.code && (
                    <div className="page-code">
                      <span className="code-tag">{detail.code}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          className="content-card"
          style={{ padding: '32px', margin: '32px auto', maxWidth: '1400px' }}
        >
          <div className="detail-layout">
            <Card loading={loading} title="基本信息" className="detail-card">
              <div className="basic-info">
                <div className="info-item">
                  <span className="info-label">名称：</span>
                  <span className="info-value">{detail?.name || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">编码：</span>
                  <span className="info-value">{detail?.code || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">所属区域：</span>
                  <span className="info-value">
                    {getRegionName(detail?.regionDictId)}
                  </span>
                </div>
                {(detail?.typeDictId || detail?.typeDict) && (
                  <div className="info-item">
                    <span className="info-label">所属类型：</span>
                    <span className="info-value">
                      {getTypeDictName(detail?.typeDictId)}
                    </span>
                  </div>
                )}

                {/* 山塬特有字段 */}
                {type === 'mountain' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">海拔高度：</span>
                      <span className="info-value">
                        {detail?.height ? `${detail.height}米` : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                  </>
                )}

                {/* 水系特有字段 */}
                {type === 'waterSystem' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">长度面积：</span>
                      <span className="info-value">
                        {detail?.lengthArea || '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                  </>
                )}

                {/* 历史要素特有字段 */}
                {type === 'historicalElement' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">建造坐标：</span>
                      <span className="info-value">
                        {detail?.constructionLongitude &&
                        detail?.constructionLatitude
                          ? `${detail.constructionLongitude}, ${detail.constructionLatitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                    {detail?.constructionTime && (
                      <div className="info-item">
                        <span className="info-label">建造时间：</span>
                        <span className="info-value">
                          {new Date(detail.constructionTime).toLocaleDateString(
                            'zh-CN',
                          )}
                        </span>
                      </div>
                    )}
                  </>
                )}

                {/* 历史记载 - 所有类型都有 */}
                {detail?.historicalRecords && (
                  <div className="info-item">
                    <span className="info-label">历史记载：</span>
                    <div className="info-value historical-records">
                      {detail.historicalRecords}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card
              loading={loading}
              title="相关图片"
              className="detail-card photos-section"
            >
              {photos?.length ? (
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: 12 }}
                >
                  {photos.map((p) => (
                    <Image
                      key={p.id}
                      src={p.url}
                      className="photo-item"
                      style={{
                        width: '100%',
                        maxHeight: 480,
                        objectFit: 'cover',
                        borderRadius: '6px',
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div
                  style={{
                    color: '#999',
                    textAlign: 'center',
                    padding: '40px 0',
                    background: '#fafafa',
                    borderRadius: '6px',
                  }}
                >
                  暂无图片
                </div>
              )}
            </Card>

            <Card
              title="关联信息"
              loading={relationsLoading}
              className="detail-card relation-info"
              styles={{ body: { padding: '16px' } }}
            >
              {relations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无关联信息"
                  className="empty-state"
                />
              ) : (
                <div>
                  {Object.entries(getGroupedRelations()).map(
                    ([relationName, relationList], index) => (
                      <div key={relationName} className="relation-group">
                        {index > 0 && <Divider style={{ margin: '16px 0' }} />}

                        <div style={{ marginBottom: 12 }}>
                          <Tag
                            color={getRelationColor(relationName)}
                            className="relation-type-tag"
                          >
                            {relationName} ({relationList.length})
                          </Tag>
                        </div>

                        <div className="relation-list">
                          {relationList.map((relation) => (
                            <div key={relation.id} className="relation-item">
                              {/* 关系主体 - 简洁的一行显示 */}
                              <div className="relation-main">
                                <div className="relation-flow">
                                  {relation.sourceElement?.name ===
                                  detail?.name ? (
                                    <>
                                      <span className="element current">
                                        {detail?.name}
                                      </span>
                                      <span className="arrow">
                                        {getRelationDirection(relation)}
                                      </span>
                                      <span className="element other">
                                        {getTargetElementName(relation)}
                                      </span>
                                    </>
                                  ) : (
                                    <>
                                      <span className="element other">
                                        {relation.sourceElement?.name ||
                                          '未知要素'}
                                      </span>
                                      <span className="arrow">
                                        {getRelationDirection(relation)}
                                      </span>
                                      <span className="element current">
                                        {detail?.name}
                                      </span>
                                    </>
                                  )}
                                </div>
                                <div className="relation-type">
                                  <Tag color="blue">{relationName}</Tag>
                                  {relation.targetEntityType && (
                                    <Tag color="default">
                                      {getTargetElementTypeName(relation)}
                                    </Tag>
                                  )}
                                </div>
                              </div>

                              {/* 详细记载 - 可选显示 */}
                              {relation.record && (
                                <div className="relation-record">
                                  <FileTextOutlined
                                    style={{ marginRight: 6, color: '#999' }}
                                  />
                                  {relation.record}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              )}
            </Card>

            {/* 关系网络图 */}
            {networkData && networkData.nodes.length > 0 && (
              <Card
                title="关系网络图"
                className="detail-card network-graph"
                styles={{ body: { padding: '16px' } }}
              >
                <NetworkGraph
                  data={networkData}
                  loading={relationsLoading}
                  title=""
                  height={500}
                />
              </Card>
            )}
          </div>
        </div>
      </PublicLayout>
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  regionList: dictionary.regionList,
  typeList: dictionary.typeList,
}))(DetailPage);
