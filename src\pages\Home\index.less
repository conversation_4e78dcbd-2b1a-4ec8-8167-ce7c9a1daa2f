/* Hero Banner */
.hero-banner {
  position: relative;
  height: calc(100vh - 72px);
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 60%) 0%,
      rgba(118, 75, 162, 60%) 50%,
      rgba(240, 147, 251, 60%) 100%
    ),
    radial-gradient(
      circle at 20% 80%,
      rgba(120, 119, 198, 20%) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 119, 198, 20%) 0%,
      transparent 50%
    ),
    url('/images/banners/home.jpg');
  background-attachment: fixed;
  background-size: cover, 600px 600px, 800px 800px, cover;
  background-position: center, 0 0, 100px 100px, center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    background-attachment: scroll;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
  }

  /* 动态粒子背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(255, 255, 255, 10%) 1px,
        transparent 1px
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(255, 255, 255, 8%) 1px,
        transparent 1px
      ),
      radial-gradient(
        circle at 50% 50%,
        rgba(255, 255, 255, 6%) 1px,
        transparent 1px
      );
    background-size: 100px 100px, 150px 150px, 200px 200px;
    background-position: 0 0, 50px 50px, 100px 100px;
    animation: float-particles 20s ease-in-out infinite;
    z-index: 1;
  }

  /* 装饰性几何元素 */
  .hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .decoration-shape {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 10%) 0%,
        rgba(255, 255, 255, 5%) 100%
      );
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 20%);

      &.decoration-shape-1 {
        width: 300px;
        height: 300px;
        top: 10%;
        right: 5%;
        animation: float-decoration-1 20s ease-in-out infinite;
      }

      &.decoration-shape-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        left: 8%;
        animation: float-decoration-2 15s ease-in-out infinite;
      }

      &.decoration-shape-3 {
        width: 100px;
        height: 100px;
        top: 20%;
        left: 15%;
        animation: float-decoration-3 25s ease-in-out infinite;
        border-radius: 20px;
      }
    }

    .decoration-line {
      position: absolute;
      background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 20%) 0%,
        transparent 100%
      );
      border-radius: 2px;

      &.decoration-line-1 {
        width: 200px;
        height: 2px;
        top: 30%;
        right: 20%;
        transform: rotate(45deg);
        animation: line-glow 3s ease-in-out infinite;
      }

      &.decoration-line-2 {
        width: 150px;
        height: 2px;
        bottom: 25%;
        left: 10%;
        transform: rotate(-30deg);
        animation: line-glow 4s ease-in-out infinite 1s;
      }
    }
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 10%) 0%,
        rgba(118, 75, 162, 10%) 100%
      ),
      radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(0, 0, 0, 10%) 100%
      );
    z-index: 2;
  }

  .hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 1200px;
    padding: 0 24px;
    animation: hero-fade-in 1.2s ease-out;

    .hero-title {
      color: white !important;
      font-size: 4rem;
      font-weight: 800;
      margin-bottom: 32px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 30%), 0 2px 4px rgba(0, 0, 0, 20%);
      line-height: 1.1;
      letter-spacing: -0.02em;
      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
        border-radius: 2px;
        animation: title-underline 1.5s ease-out 0.5s both;
      }

      @media (max-width: 768px) {
        font-size: 2.8rem;
      }
    }

    .hero-description {
      font-size: 1.4rem;
      margin-bottom: 40px;
      color: rgba(255, 255, 255, 95%);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 30%);
      line-height: 1.6;
      font-weight: 300;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 1.2rem;
        margin-bottom: 32px;
      }
    }

    .hero-statistics {
      margin: 48px auto;
      max-width: 900px;

      .stats-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;
        flex-wrap: wrap;

        @media (max-width: 768px) {
          gap: 24px;
          justify-content: space-around;
        }

        @media (max-width: 480px) {
          gap: 20px;
          justify-content: space-between;
        }
      }

      .stat-item {
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        padding: 16px 14px;
        border-radius: 12px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 10%) 0%,
          rgba(255, 255, 255, 5%) 100%
        );
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 12%);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 8%),
          inset 0 1px 0 rgba(255, 255, 255, 15%);
        min-width: 110px;

        /* 添加微妙的内发光效果 */
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 12px;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 8%) 0%,
            transparent 50%,
            rgba(255, 255, 255, 4%) 100%
          );
          pointer-events: none;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-3px) scale(1.01);
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 15%) 0%,
            rgba(255, 255, 255, 7%) 100%
          );
          border-color: rgba(255, 255, 255, 20%);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 12%),
            0 4px 12px rgba(0, 0, 0, 8%), inset 0 1px 0 rgba(255, 255, 255, 25%);

          &::before {
            opacity: 1;
          }

          .stat-value {
            text-shadow: 0 1px 8px rgba(255, 255, 255, 20%);
            transform: scale(1.03);
          }

          .stat-label {
            color: rgba(255, 255, 255, 90%);
            text-shadow: 0 1px 3px rgba(0, 0, 0, 20%);
          }

          /* 悬停时的特殊效果 */
          .stat-value::after {
            opacity: 0.6;
          }

          .stat-label::after {
            width: 80%;
          }
        }

        .stat-number {
          margin-bottom: 12px;
          line-height: 1;
          position: relative;

          .stat-value {
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(
              135deg,
              #fff 0%,
              #e6f7ff 30%,
              #bae7ff 60%,
              #91d5ff 100%
            );
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            display: inline-block;

            /* 添加数字发光效果 */
            &::after {
              content: attr(data-value);
              position: absolute;
              top: 0;
              left: 0;
              background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 80%) 0%,
                rgba(230, 247, 255, 60%) 100%
              );
              background-clip: text;
              -webkit-text-fill-color: transparent;
              filter: blur(8px);
              opacity: 0;
              transition: opacity 0.3s ease;
              z-index: -1;
            }

            @media (max-width: 768px) {
              font-size: 30px;
            }

            @media (max-width: 480px) {
              font-size: 26px;
            }
          }

          .stat-suffix {
            font-size: 16px;
            margin-left: 4px;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 85%) 0%,
              rgba(255, 255, 255, 65%) 100%
            );
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 500;
            text-shadow: none;
            transition: all 0.3s ease;

            @media (max-width: 768px) {
              font-size: 14px;
            }

            @media (max-width: 480px) {
              font-size: 12px;
            }
          }
        }

        .stat-label {
          font-size: 13px;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 80%) 0%,
            rgba(255, 255, 255, 60%) 100%
          );
          background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 500;
          text-shadow: none;
          transition: all 0.3s ease;
          letter-spacing: 0.5px;
          position: relative;

          /* 添加下划线装饰 */
          &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: linear-gradient(
              90deg,
              transparent 0%,
              rgba(255, 255, 255, 60%) 50%,
              transparent 100%
            );
            transition: width 0.3s ease;
          }

          @media (max-width: 768px) {
            font-size: 12px;
            letter-spacing: 0.3px;
          }

          @media (max-width: 480px) {
            font-size: 11px;
            letter-spacing: 0.2px;
          }
        }
      }

      @media (max-width: 768px) {
        margin: 40px auto;

        .stat-item {
          padding: 20px 16px;
          min-width: 120px;
          border-radius: 12px;

          .stat-number {
            margin-bottom: 12px;
          }

          .stat-label {
            font-size: 13px;
          }
        }
      }

      @media (max-width: 480px) {
        margin: 32px auto;

        .stats-container {
          gap: 16px;
        }

        .stat-item {
          padding: 16px 12px;
          min-width: 100px;
          border-radius: 10px;

          .stat-number {
            margin-bottom: 10px;

            .stat-value {
              font-size: 32px;
            }

            .stat-suffix {
              font-size: 14px;
            }
          }

          .stat-label {
            font-size: 12px;
            letter-spacing: 0.2px;
          }
        }
      }
    }

    .hero-actions {
      .ant-btn {
        height: 56px;
        padding: 0 40px;
        font-size: 18px;
        font-weight: 600;
        border-radius: 28px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 15%), 0 4px 8px rgba(0, 0, 0, 10%);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 20%),
            transparent
          );
          transition: left 0.6s;
        }

        &:hover {
          transform: translateY(-4px) scale(1.05);
          box-shadow: 0 16px 40px rgba(0, 0, 0, 20%),
            0 8px 16px rgba(0, 0, 0, 15%);

          &::before {
            left: 100%;
          }
        }

        @media (max-width: 768px) {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
        }
      }

      .ant-btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
        color: white;
      }

      .ant-btn:not(.ant-btn-primary) {
        background: rgba(255, 255, 255, 15%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 30%);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 25%);
          border-color: rgba(255, 255, 255, 50%);
        }
      }
    }
  }

  /* 向下滚动指示器 */
  .scroll-indicator {
    position: absolute;
    display: flex;
    gap: 8px;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 4;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: scroll-bounce 3s ease-in-out infinite;
    background: rgba(255, 255, 255, 8%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 15%);
    border-radius: 30px;
    padding: 12px 20px;
    min-width: 140px;

    &:hover {
      transform: translateX(-50%) translateY(-6px);
      background: rgba(255, 255, 255, 15%);
      border-color: rgba(255, 255, 255, 25%);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 15%);

      .scroll-text {
        color: rgba(255, 255, 255, 95%);
      }

      .scroll-arrow {
        transform: scale(1.1);
        color: #40a9ff;
      }
    }

    .scroll-text {
      font-size: 13px;
      color: rgba(255, 255, 255, 75%);
      margin-bottom: 4px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 20%);
      transition: all 0.3s ease;
      letter-spacing: 0.3px;
    }

    .scroll-arrow {
      font-size: 16px;
      color: rgba(255, 255, 255, 80%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 20%));
      animation: arrow-float 2s ease-in-out infinite;
    }

    @media (max-width: 768px) {
      bottom: 20px;
      padding: 10px 16px;
      min-width: 120px;

      .scroll-text {
        font-size: 12px;
        margin-bottom: 3px;
      }

      .scroll-arrow {
        font-size: 14px;
      }
    }
  }
}

/* 特色功能区域 */
.features-section {
  height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%),
    radial-gradient(
      circle at 70% 30%,
      rgba(82, 196, 26, 5%) 0%,
      transparent 50%
    );
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(
        circle at 20% 20%,
        rgba(24, 144, 255, 3%) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(250, 140, 22, 3%) 0%,
        transparent 50%
      );
    background-size: 800px 800px, 600px 600px;
    pointer-events: none;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
  }

  .section-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #fa8c16 100%);
      border-radius: 2px;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 800;
      color: #333;
      margin-bottom: 24px;
      position: relative;
      background: linear-gradient(135deg, #333 0%, #666 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .section-description {
      font-size: 1.2rem;
      color: #666;
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.6;
      font-weight: 300;
    }
  }

  .feature-card {
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 8%), 0 6px 20px rgba(0, 0, 0, 4%),
      inset 0 1px 0 rgba(255, 255, 255, 80%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 50%);
    height: 100%;
    background: rgba(255, 255, 255, 90%);
    backdrop-filter: blur(20px);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 10%) 0%,
        rgba(255, 255, 255, 5%) 100%
      );
      pointer-events: none;
      z-index: 1;
    }

    .feature-image {
      position: relative;
      height: 240px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 20%) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 70% 70%,
            rgba(255, 255, 255, 10%) 0%,
            transparent 50%
          );
        z-index: 1;
      }

      .feature-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 10%);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 2;

        .feature-icon {
          font-size: 64px;
          color: white;
          transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 30%));
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 10%);
            border-radius: 50%;
            z-index: -1;
            transition: all 0.5s ease;
          }
        }
      }

      &:hover {
        transform: scale(1.05);

        .feature-overlay {
          background: rgba(0, 0, 0, 20%);

          .feature-icon {
            font-size: 72px;
            transform: scale(1.1) rotate(5deg);

            &::before {
              width: 140px;
              height: 140px;
              background: rgba(255, 255, 255, 20%);
            }
          }
        }
      }
    }

    .ant-card-body {
      padding: 32px 24px;
      position: relative;
      z-index: 2;

      .feature-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 16px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 0;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
          transition: width 0.4s ease;
        }

        .feature-arrow {
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          color: #1890ff;
          font-size: 18px;
          transform: translateX(-10px);
        }
      }

      .ant-card-meta-description {
        color: #666;
        line-height: 1.7;
        margin-top: 16px;
        font-size: 15px;
        font-weight: 400;
      }
    }

    &:hover {
      transform: translateY(-12px) scale(1.02);
      box-shadow: 0 24px 60px rgba(0, 0, 0, 15%), 0 12px 30px rgba(0, 0, 0, 10%),
        inset 0 1px 0 rgba(255, 255, 255, 90%);

      .feature-title {
        &::after {
          width: 60px;
        }

        .feature-arrow {
          opacity: 1;
          transform: translateX(0);
        }
      }
    }
  }
}

/* 地图展示区域 */
.map-section {
  height: calc(100vh - 72px);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
    radial-gradient(
      circle at 50% 50%,
      rgba(24, 144, 255, 5%) 0%,
      transparent 50%
    );
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #1890ff 50%,
      transparent 100%
    );
  }

  .container {
    width: 100%;
    max-width: 2400px;
    margin: 0 auto;
    padding: 40px 24px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .section-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    flex-shrink: 0;
    padding-top: 20px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #fa8c16 100%);
      border-radius: 2px;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 800;
      color: #333;
      margin-bottom: 24px;
      background: linear-gradient(135deg, #333 0%, #666 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .section-description {
      font-size: 1.2rem;
      color: #666;
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.6;
      font-weight: 300;
    }
  }

  .map-card {
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 10%), 0 10px 30px rgba(0, 0, 0, 5%),
      inset 0 1px 0 rgba(255, 255, 255, 80%);
    border: 1px solid rgba(255, 255, 255, 50%);
    background: rgba(255, 255, 255, 90%);
    backdrop-filter: blur(20px);
    position: relative;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    display: flex;
    flex-direction: column;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 10%) 0%,
        rgba(255, 255, 255, 5%) 100%
      );
      pointer-events: none;
      z-index: 1;
    }

    .ant-card-body {
      padding: 0;
      position: relative;
      z-index: 2;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .map-container {
      width: 100%;
      flex: 1;
      min-height: 600px;
      border-radius: 24px;
      overflow: hidden;
      position: relative;

      @media (max-width: 768px) {
        min-height: 450px;
      }

      /* 地图图例样式 */
      .map-legend {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 95%);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 10%);
        border: 1px solid rgba(255, 255, 255, 30%);
        z-index: 1000;
        min-width: 120px;

        .legend-title {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
          text-align: center;
          border-bottom: 1px solid rgba(0, 0, 0, 10%);
          padding-bottom: 8px;
        }

        .legend-items {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;
          color: #666;
          transition: all 0.3s ease;

          &:hover {
            color: #333;
            transform: translateX(2px);
          }

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 20%);
            flex-shrink: 0;
          }

          span {
            font-weight: 500;
            white-space: nowrap;
          }
        }

        @media (max-width: 768px) {
          top: 10px;
          right: 10px;
          padding: 12px;
          min-width: 100px;

          .legend-title {
            font-size: 12px;
            margin-bottom: 8px;
          }

          .legend-item {
            font-size: 11px;
            gap: 6px;

            .legend-color {
              width: 10px;
              height: 10px;
            }
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-banner {
    .hero-decorations {
      .decoration-shape {
        &.decoration-shape-1 {
          width: 200px;
          height: 200px;
        }

        &.decoration-shape-2 {
          width: 100px;
          height: 100px;
        }

        &.decoration-shape-3 {
          width: 80px;
          height: 80px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .hero-banner {
    padding: 0 16px;

    .hero-decorations {
      display: none; /* 在小屏幕上隐藏装饰元素以提高性能 */
    }

    .hero-content {
      padding: 0 16px;

      .hero-title {
        font-size: 2.5rem !important;

        &::after {
          width: 60px;
        }
      }

      .hero-description {
        font-size: 1.1rem !important;
      }

      .hero-statistics {
        margin: 32px auto;
      }

      .hero-actions {
        flex-direction: column;
        gap: 16px;

        .ant-btn {
          width: 100%;
          max-width: 280px;
        }
      }
    }
  }

  .features-section,
  .map-section {
    .container {
      padding: 30px 16px;
    }

    .section-decorations {
      display: none; /* 在小屏幕上隐藏装饰元素 */
    }
  }

  .features-section {
    .section-header {
      margin-bottom: 40px;

      .section-title {
        font-size: 2.2rem;
      }

      .section-description {
        font-size: 1rem;
      }
    }

    .feature-card {
      margin-bottom: 24px;

      .feature-image {
        height: 180px;

        .feature-overlay {
          .feature-icon {
            font-size: 48px !important;

            &:hover {
              font-size: 52px !important;
            }
          }
        }
      }

      .ant-card-body {
        padding: 20px 16px;

        .feature-title {
          font-size: 1.2rem;
        }

        .ant-card-meta-description {
          font-size: 14px;
        }
      }
    }
  }

  .map-section {
    .section-header {
      margin-bottom: 40px;

      .section-title {
        font-size: 2.2rem;
      }

      .section-description {
        font-size: 1rem;
      }
    }

    .map-card {
      .map-container {
        height: 400px !important;
      }
    }
  }

  .footer-section {
    .container {
      padding: 30px 16px;
    }

    .footer-links,
    .footer-contact {
      margin-top: 32px;

      h5 {
        font-size: 16px;
      }
    }

    .footer-nav {
      gap: 12px !important;

      a {
        padding: 6px 0 !important;
        font-size: 14px;
      }
    }

    .footer-info {
      p {
        font-size: 14px;
        margin-bottom: 8px !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .hero-banner {
    .hero-content {
      .hero-title {
        font-size: 2rem !important;
        line-height: 1.2;
      }

      .hero-description {
        font-size: 1rem !important;
        margin-bottom: 32px;
      }
    }
  }

  .features-section {
    .section-header {
      .section-title {
        font-size: 1.8rem;
      }
    }
  }

  .map-section {
    .section-header {
      .section-title {
        font-size: 1.8rem;
      }
    }

    .map-card {
      .map-container {
        height: 300px !important;
      }
    }
  }
}

/* 动画效果 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hero-fade-in {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes title-underline {
  0% {
    width: 0;
    opacity: 0;
  }

  100% {
    width: 100px;
    opacity: 1;
  }
}

@keyframes float-particles {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  33% {
    transform: translateY(-20px) rotate(120deg);
  }

  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

@keyframes rotate-slow {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 30%);
  }

  50% {
    box-shadow: 0 0 40px rgba(24, 144, 255, 60%);
  }
}

/* 装饰元素动画 */
@keyframes float-decoration-1 {
  0%,
  100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }

  33% {
    transform: translateY(-30px) rotate(120deg) scale(1.05);
  }

  66% {
    transform: translateY(15px) rotate(240deg) scale(0.95);
  }
}

@keyframes float-decoration-2 {
  0%,
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }

  50% {
    transform: translateX(20px) translateY(-20px) rotate(180deg);
  }
}

@keyframes float-decoration-3 {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-25px) scale(1.1);
  }
}

@keyframes line-glow {
  0%,
  100% {
    opacity: 0.3;
    box-shadow: 0 0 10px rgba(255, 255, 255, 30%);
  }

  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px rgba(255, 255, 255, 60%);
  }
}

@keyframes float-gentle-1 {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

@keyframes float-gentle-2 {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }

  50% {
    transform: translateX(10px) translateY(-10px);
  }
}

@keyframes float-gentle-3 {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-12px) scale(1.05);
  }
}

/* 渐变背景动画 */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes scroll-bounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }

  50% {
    transform: translateX(-50%) translateY(-8px);
  }
}

@keyframes scroll-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 40%);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0%);
  }
}

@keyframes arrow-float {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.8;
  }

  50% {
    transform: translateY(-3px);
    opacity: 1;
  }
}

.hero-content,
.feature-card,
.map-card {
  animation: fade-in-up 0.6s ease-out;
}

.stat-item {
  animation: fade-in-up 0.8s ease-out;

  &:nth-child(1) {
    animation-delay: 0.2s;
  }

  &:nth-child(2) {
    animation-delay: 0.3s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }

  &:nth-child(4) {
    animation-delay: 0.5s;
  }
}

.feature-card {
  &:nth-child(1) {
    animation-delay: 0.2s;
  }

  &:nth-child(2) {
    animation-delay: 0.3s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

.map-card {
  animation-delay: 0.5s;
}

/* 页脚区域 */
.footer-section {
  height: calc(100vh - 72px);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%),
    radial-gradient(
      circle at 30% 70%,
      rgba(52, 73, 94, 80%) 0%,
      transparent 50%
    );
  color: white;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(
        circle at 20% 20%,
        rgba(255, 255, 255, 5%) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(255, 255, 255, 3%) 0%,
        transparent 50%
      );
    background-size: 600px 600px, 800px 800px;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #fa8c16 100%);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 24px;
    position: relative;
    z-index: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .footer-brand {
    .ant-typography {
      color: white;
    }

    h4 {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
        border-radius: 2px;
      }
    }
  }

  .footer-links {
    .footer-nav {
      display: flex;
      flex-direction: column;
      gap: 16px;

      a {
        color: rgba(255, 255, 255, 80%);
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        padding: 8px 0;
        position: relative;
        font-weight: 400;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          bottom: 0;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
          transition: width 0.3s ease;
        }

        &:hover {
          color: #1890ff;
          transform: translateX(8px);

          &::before {
            width: 30px;
          }
        }
      }
    }

    h5 {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, #52c41a 0%, #fa8c16 100%);
        border-radius: 1px;
      }
    }
  }

  .footer-contact {
    .footer-info {
      p {
        color: rgba(255, 255, 255, 80%);
        margin-bottom: 12px;
        line-height: 1.6;
        font-weight: 300;
        position: relative;
        padding-left: 20px;

        &::before {
          content: '▸';
          position: absolute;
          left: 0;
          color: #1890ff;
          font-weight: bold;
        }
      }
    }

    h5 {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, #fa8c16 0%, #1890ff 100%);
        border-radius: 1px;
      }
    }
  }

  .footer-bottom {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 10%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        #1890ff 50%,
        transparent 100%
      );
    }
  }
}

/* 回到顶部按钮 */
.back-top-button {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 30%);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(24, 144, 255, 40%);
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 全局动画优化 */
* {
  transition: all 0.3s ease;
}
