/**
 * 高德地图工具函数
 */

/**
 * 将经纬度坐标转换为像素坐标
 * @param map 地图实例
 * @param position 经纬度坐标 [经度, 纬度]
 * @returns 像素坐标 {x, y}
 */
export const lngLatToPixel = (map: any, position: [number, number]) => {
  if (!map) return { x: 0, y: 0 };
  const lngLat = new (window as any).AMap.LngLat(position[0], position[1]);
  return map.lngLatToContainer(lngLat);
};

/**
 * 将像素坐标转换为经纬度坐标
 * @param map 地图实例
 * @param pixel 像素坐标 {x, y}
 * @returns 经纬度坐标 [经度, 纬度]
 */
export const pixelToLngLat = (map: any, pixel: { x: number; y: number }) => {
  if (!map) return [0, 0] as [number, number];
  const lngLat = map.containerToLngLat(
    new (window as any).AMap.Pixel(pixel.x, pixel.y),
  );
  return [lngLat.getLng(), lngLat.getLat()] as [number, number];
};

/**
 * 计算两点之间的距离（米）
 * @param position1 起点坐标 [经度, 纬度]
 * @param position2 终点坐标 [经度, 纬度]
 * @returns 距离（米）
 */
export const calculateDistance = (
  position1: [number, number],
  position2: [number, number],
) => {
  const AMap = (window as any).AMap;
  if (!AMap) return 0;

  const lnglat1 = new AMap.LngLat(position1[0], position1[1]);
  const lnglat2 = new AMap.LngLat(position2[0], position2[1]);
  return lnglat1.distance(lnglat2);
};

/**
 * 根据标记点状态获取图标URL
 * @param status 标记点状态
 * @returns 图标URL
 */
export const getMarkerIconByStatus = (status?: number) => {
  // 根据不同状态返回不同的图标URL
  switch (status) {
    case 1: // 正常
      return '/images/markers/mountain.png';
    case 2: // 警告
      return '/images/markers/water.png';
    case 3: // 错误
      return '/images/markers/historical.png';
    default: // 默认
      return '/images/markers/default.png';
  }
};

/**
 * 创建自定义图标样式
 * @param color 图标颜色
 * @param size 图标大小
 * @returns HTML字符串
 */
export const createCustomMarkerStyle = (
  color: string = '#3498db',
  size: number = 12,
) => {
  return `<div style="background-color: ${color}; width: ${size}px; height: ${size}px; border: 2px solid white; border-radius: 50%; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>`;
};

/**
 * 创建聚合点样式
 * @param count 聚合点数量
 * @param color 聚合点颜色
 * @returns HTML字符串
 */
export const createClusterStyle = (
  count: number,
  color: string = 'rgba(0, 161, 214, 0.8)',
) => {
  return `<div style="background-color: ${color}; color: white; width: 36px; height: 36px; line-height: 36px; text-align: center; border-radius: 50%">${count}</div>`;
};

/**
 * 获取地图视野范围内的标记点
 * @param map 地图实例
 * @param markers 所有标记点
 * @returns 视野范围内的标记点
 */
export const getMarkersInView = (map: any, markers: Marker[]) => {
  if (!map) return [];

  const bounds = map.getBounds();
  return markers.filter((marker) => {
    const position = marker.position;
    const lnglat = new (window as any).AMap.LngLat(position[0], position[1]);
    return bounds.contains(lnglat);
  });
};

/**
 * 确保高德地图API已加载
 * @returns Promise<any> 高德地图API对象
 */
const ensureAMapLoaded = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    // 如果已经加载，直接返回
    if ((window as any).AMap) {
      resolve((window as any).AMap);
      return;
    }

    // 动态加载高德地图API
    import('@amap/amap-jsapi-loader')
      .then((AMapLoader) => {
        // 设置安全密钥
        (window as any)._AMapSecurityConfig = {
          securityJsCode: AMAP_CONFIG.securityJsCode,
        };

        // 加载高德地图JS API
        AMapLoader.default
          .load({
            key: AMAP_CONFIG.key,
            version: '2.0',
            plugins: ['AMap.Geocoder'], // 只加载必要的插件
          })
          .then((AMap) => {
            resolve(AMap);
          })
          .catch((error) => {
            reject(new Error(`高德地图API加载失败: ${error.message}`));
          });
      })
      .catch((error) => {
        reject(new Error(`AMapLoader导入失败: ${error.message}`));
      });
  });
};

/**
 * 逆地理编码：将经纬度坐标转换为地址
 * @param longitude 经度
 * @param latitude 纬度
 * @returns Promise<string> 地址字符串
 */
export const reverseGeocode = async (
  longitude: number | string,
  latitude: number | string,
): Promise<string> => {
  // 确保高德地图API已加载
  const AMap = await ensureAMapLoaded();

  // 验证坐标有效性
  const lng = parseFloat(longitude.toString());
  const lat = parseFloat(latitude.toString());

  if (isNaN(lng) || isNaN(lat)) {
    throw new Error('坐标格式无效');
  }

  // 创建逆地理编码实例
  const geocoder = new AMap.Geocoder({
    radius: 1000,
    extensions: 'all',
  });

  const lnglat = new AMap.LngLat(lng, lat);

  // 使用Promise包装回调
  return new Promise((resolve, reject) => {
    geocoder.getAddress(lnglat, (status: string, result: any) => {
      if (status === 'complete' && result && result.regeocode) {
        const address = result.regeocode.formattedAddress;
        resolve(address || '未知地址');
      } else {
        reject(new Error(`地址解析失败: ${status}`));
      }
    });
  });
};

/**
 * 详细逆地理编码：将经纬度坐标转换为详细地址信息
 * @param longitude 经度
 * @param latitude 纬度
 * @returns Promise<{address: string, addressDetail: string, fullInfo: any}> 详细地址信息
 */
export const reverseGeocodeDetailed = async (
  longitude: number | string,
  latitude: number | string,
): Promise<{
  address: string;
  addressDetail: string;
  fullInfo: any;
}> => {
  // 确保高德地图API已加载
  const AMap = await ensureAMapLoaded();

  // 验证坐标有效性
  const lng = parseFloat(longitude.toString());
  const lat = parseFloat(latitude.toString());

  if (isNaN(lng) || isNaN(lat)) {
    throw new Error('坐标格式无效');
  }

  // 创建逆地理编码实例
  const geocoder = new AMap.Geocoder({
    radius: 1000,
    extensions: 'all',
  });

  const lnglat = new AMap.LngLat(lng, lat);

  // 使用Promise包装回调
  return new Promise((resolve, reject) => {
    geocoder.getAddress(lnglat, (status: string, result: any) => {
      if (status === 'complete' && result && result.regeocode) {
        const regeocode = result.regeocode;

        // 基本地址（省市区街道）
        const basicAddress = regeocode.formattedAddress || '未知地址';

        // 详细地址（门牌号、建筑物名称等）
        let detailAddress = '';

        // 尝试获取更详细的地址信息
        if (regeocode.addressComponent) {
          const component = regeocode.addressComponent;
          const parts = [];

          // 添加建筑物名称
          if (component.building && component.building !== '[]') {
            parts.push(component.building);
          }

          // 添加门牌号
          if (component.streetNumber && component.streetNumber.number) {
            parts.push(component.streetNumber.number);
          }

          // 添加POI信息
          if (regeocode.pois && regeocode.pois.length > 0) {
            const nearestPoi = regeocode.pois[0];
            if (nearestPoi.name && !parts.includes(nearestPoi.name)) {
              parts.push(nearestPoi.name);
            }
          }

          detailAddress = parts.join('');
        }

        resolve({
          address: basicAddress,
          addressDetail: detailAddress,
          fullInfo: regeocode,
        });
      } else {
        reject(new Error(`地址解析失败: ${status}`));
      }
    });
  });
};

/**
 * 批量逆地理编码
 * @param coordinates 坐标数组 [{longitude, latitude}]
 * @returns Promise<string[]> 地址数组
 */
export const batchReverseGeocode = async (
  coordinates: Array<{ longitude: number | string; latitude: number | string }>,
): Promise<string[]> => {
  const promises = coordinates.map((coord) =>
    reverseGeocode(coord.longitude, coord.latitude).catch(() => '地址解析失败'),
  );
  return Promise.all(promises);
};

/**
 * 地理编码：将地址转换为经纬度坐标
 * @param address 地址字符串
 * @param city 城市名称（可选，用于提高搜索精度）
 * @returns Promise<{longitude: number, latitude: number, formattedAddress: string}[]> 坐标数组
 */
export const geocode = async (
  address: string,
  city?: string,
): Promise<
  Array<{
    longitude: number;
    latitude: number;
    formattedAddress: string;
    district?: string;
    township?: string;
  }>
> => {
  // 确保高德地图API已加载
  const AMap = await ensureAMapLoaded();

  if (!address || !address.trim()) {
    throw new Error('地址不能为空');
  }

  // 创建地理编码实例
  const geocoder = new AMap.Geocoder({
    city: city || '西安', // 默认城市为西安
    radius: 1000,
    extensions: 'all',
  });

  // 使用Promise包装回调
  return new Promise((resolve, reject) => {
    geocoder.getLocation(address.trim(), (status: string, result: any) => {
      if (
        status === 'complete' &&
        result &&
        result.geocodes &&
        result.geocodes.length > 0
      ) {
        const geocodes = result.geocodes.map((item: any) => ({
          longitude: parseFloat(item.location.lng),
          latitude: parseFloat(item.location.lat),
          formattedAddress: item.formattedAddress || address,
          district: item.district,
          township: item.township,
        }));
        resolve(geocodes);
      } else {
        reject(new Error(`地址搜索失败: ${status}`));
      }
    });
  });
};
